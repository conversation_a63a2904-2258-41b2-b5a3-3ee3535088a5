const cron = require('node-cron');
const { Op, Transaction } = require('sequelize');
const moment = require('moment');
const { sequelize } = require('../Database/Schemas');
const UserSubscriptionModel = require('../Models/App/UserSubscriptionModel');
const ButtonModel = require('../Models/App/ButtonModel');
const ItemModel = require('../Models/App/ItemModel');
const AlarmModel = require('../Models/App/AlarmModel');

// Initialize model instances
const userSubscriptionModel = new UserSubscriptionModel();
const buttonModel = new ButtonModel();
const itemModel = new ItemModel();
const alarmModel = new AlarmModel();

// Global flag to prevent overlapping executions
let isJobRunning = false;

// Enhanced logging function
const logWithTimestamp = (level, message, data = null) => {
  const timestamp = moment().format('YYYY-MM-DD HH:mm:ss');
  const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`;

  if (level === 'error') {
    console.error(logMessage, data ? JSON.stringify(data, null, 2) : '');
  } else {
    console.log(logMessage, data ? JSON.stringify(data, null, 2) : '');
  }
};

// Function to safely delete user button data (keeping user and subscription intact)
const deleteUserButtonData = async (userId, transaction) => {
  try {
    logWithTimestamp(
      'info',
      `Starting button data deletion for user ID: ${userId}`
    );

    // Get count of buttons and related data before deletion for reporting
    const buttonCount = await buttonModel.countButtons(
      { user_id: userId },
      true, // includeDeleted
      { transaction }
    );

    if (buttonCount === 0) {
      logWithTimestamp('info', `No buttons found for user ${userId}`);
      return {
        userId,
        deletedButtons: 0,
        message: 'No button data to delete',
      };
    }

    // Get counts of related data for reporting (before deletion)
    const itemCount = await itemModel.countItems(
      { user_id: userId },
      true, // includeDeleted
      { transaction }
    );

    const alarmCount = await alarmModel.countAlarms(
      { user_id: userId },
      true, // includeDeleted
      { transaction }
    );

    logWithTimestamp(
      'info',
      `Found data for user ${userId}: ${buttonCount} buttons, ${itemCount} items, ${alarmCount} alarms`
    );

    // Delete buttons - this will CASCADE delete all related data automatically:
    // - Items (button_items table with foreign key to buttons)
    // - Alarms (alarms table with foreign key to buttons)
    // - Tags (item_tags table with foreign key to items, which reference buttons)
    const deletedButtons = await buttonModel.destroyButtons(
      { user_id: userId },
      {
        transaction,
        force: true, // Hard delete (bypass soft delete if enabled)
      }
    );

    logWithTimestamp(
      'info',
      `Successfully deleted ${deletedButtons} buttons for user ${userId}`
    );
    logWithTimestamp(
      'info',
      `CASCADE deletion automatically removed all related items, alarms, and tags for user ${userId}`
    );

    return {
      userId,
      deletedButtons,
      originalButtonCount: buttonCount,
      originalItemCount: itemCount,
      originalAlarmCount: alarmCount,
      message:
        'Button data and all related records deleted successfully via CASCADE',
    };
  } catch (error) {
    logWithTimestamp(
      'error',
      `Failed to delete button data for user ${userId}`,
      error
    );
    throw error;
  }
};

// Main cron job function
const runUserButtonDataDeletionJob = async () => {
  // Prevent overlapping executions
  if (isJobRunning) {
    logWithTimestamp(
      'warn',
      'User button data deletion job is already running, skipping this execution'
    );
    return;
  }

  isJobRunning = true;
  const jobStartTime = moment();
  logWithTimestamp('info', 'Starting daily user button data deletion cron job');

  let transaction;
  try {
    // Use UTC time to avoid timezone issues
    const currentTime = moment.utc().toDate();
    logWithTimestamp(
      'info',
      `Checking for users with deletion_date <= ${currentTime.toISOString()}`
    );

    // Step 1: Find all user IDs with a deletion_date that has passed
    const expiredSubs = await userSubscriptionModel.findAllSubscriptions(
      {
        deletion_date: {
          [Op.lte]: currentTime,
        },
        data_deleted: false,
      },
      {
        attributes: ['user_id', 'deletion_date'],
        raw: true,
      }
    );

    const userIdsToProcess = [
      ...new Set(expiredSubs.map((sub) => sub.user_id)),
    ];

    if (userIdsToProcess.length === 0) {
      logWithTimestamp('info', 'No users found for button data deletion');
      return;
    }

    logWithTimestamp(
      'info',
      `Found ${userIdsToProcess.length} user(s) for button data deletion`,
      { userIds: userIdsToProcess }
    );

    // Start transaction for data consistency
    transaction = await sequelize.transaction({
      isolationLevel: Transaction.ISOLATION_LEVELS.READ_COMMITTED,
    });

    const deletionResults = [];
    let successCount = 0;
    let failureCount = 0;
    let totalButtonsDeleted = 0;
    let totalItemsDeleted = 0;
    let totalAlarmsDeleted = 0;

    // Process each user's button data deletion
    for (const userId of userIdsToProcess) {
      try {
        const result = await deleteUserButtonData(userId, transaction);
        deletionResults.push(result);
        successCount++;
        totalButtonsDeleted += result.deletedButtons || 0;
        totalItemsDeleted += result.originalItemCount || 0;
        totalAlarmsDeleted += result.originalAlarmCount || 0;
        await userSubscriptionModel.updateSubscription(
          { data_deleted: true },
          {
            user_id: userId,
            deletion_date: {
              [Op.lte]: currentTime,
            },
            data_deleted: false,
          },
          { transaction }
        );
        logWithTimestamp(
          'info',
          `Successfully deleted button data for user ${userId}`
        );
      } catch (error) {
        failureCount++;
        logWithTimestamp(
          'error',
          `Failed to delete button data for user ${userId}`,
          error
        );
        // Continue with other users instead of failing the entire job
      }
    }

    // Commit transaction if at least one user was successfully processed
    if (successCount > 0) {
      await transaction.commit();
      logWithTimestamp(
        'info',
        `Button data deletion job completed successfully. Success: ${successCount}, Failures: ${failureCount}`
      );
    } else {
      await transaction.rollback();
      logWithTimestamp(
        'error',
        'All button data deletions failed, rolling back transaction'
      );
    }

    // Log summary
    const jobEndTime = moment();
    const duration = jobEndTime.diff(jobStartTime, 'seconds');
    logWithTimestamp('info', `Job execution summary`, {
      duration: `${duration} seconds`,
      totalUsers: userIdsToProcess.length,
      successfulDeletions: successCount,
      failedDeletions: failureCount,
      totalButtonsDeleted,
      totalItemsDeleted,
      totalAlarmsDeleted,
      startTime: jobStartTime.format('YYYY-MM-DD HH:mm:ss'),
      endTime: jobEndTime.format('YYYY-MM-DD HH:mm:ss'),
    });
  } catch (error) {
    logWithTimestamp(
      'error',
      'Critical error in button data deletion cron job',
      error
    );

    // Rollback transaction if it exists
    if (transaction) {
      try {
        await transaction.rollback();
        logWithTimestamp('info', 'Transaction rolled back due to error');
      } catch (rollbackError) {
        logWithTimestamp(
          'error',
          'Failed to rollback transaction',
          rollbackError
        );
      }
    }
  } finally {
    isJobRunning = false;
    logWithTimestamp('info', 'User button data deletion cron job finished');
  }
};

// Schedule the cron job to run daily at 2:00 AM UTC
// This prevents running during peak hours and ensures consistent timing
// cron.schedule('0 2 * * *', runUserButtonDataDeletionJob, {
//   scheduled: true,
//   timezone: "UTC"
// });

// Schedule the cron job to run every 5 minutes for testing TODO: REMOVE THIS LATER
cron.schedule('*/5 * * * *', runUserButtonDataDeletionJob, {
  scheduled: true,
  timezone: "UTC"
});

// Export for testing purposes
module.exports = {
  runUserButtonDataDeletionJob,
  deleteUserButtonData,

  // Legacy exports for backward compatibility
  runUserDeletionJob: runUserButtonDataDeletionJob,
  deleteUserWithRelatedData: deleteUserButtonData
};
