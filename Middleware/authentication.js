const jwt = require('jsonwebtoken');

const { validationResult } = require('express-validator');
const { JWT } = require('../Configs/constants');
const { API } = require('../Configs/message');
const AuthModal = new (require('../Models/App/AuthModel'))();

exports.authentication = async (request, response, next) => {
  const errors = validationResult(request).formatWith(({ msg }) => msg);

  if (!errors.isEmpty()) {
    if (errors.array()?.length) {
      console.log(
        '=================================================================\n'
      );
      console.log(
        'authentication ~ request.originalUrl: ',
        request.originalUrl
      );
      console.log('authentication ~ request.method: ', request.method);
      console.log('authentication ~ request.query: ', request.query);
      console.log('authentication ~ request.body: ', request.body);
      console.log('authentication ~ errors.array(): ', errors.array());
      console.log(
        '=================================================================\n'
      );
      console.log('\n');
    }

    return response.handler.badRequest(undefined, errors.array());
  }

  // USED FOR WHEN AUTHENTICATION IS OPTIONAL
  // if (
  //   !request.headers.authorization ||
  //   request.originalUrl.includes('/app-api/auth/signUp') ||
  //   request.originalUrl.includes('/app-api/auth/signIn') ||
  //   request.originalUrl.includes('/app-api/auth/forgotPassword') ||
  //   request.originalUrl.includes('/app-api/auth/resetPassword')
  // ) {
  //   return next();
  // }
  if (
    // !request.headers.authorization ||
    request.originalUrl.includes('/app/v1/auth/signUp') ||
    request.originalUrl.includes('/app/v1/auth/signIn') ||
    request.originalUrl.includes('/app/v1/auth/verifyOtp') ||
    request.originalUrl.includes('/app/v1/auth/resendOtp') ||
    request.originalUrl.includes('/app/v1/auth/forgotPassword') ||
    request.originalUrl.includes('/app/v1/auth/resetPassword') ||
    request.originalUrl.includes('/app/v1/auth/checkUser') ||
    request.originalUrl.includes('/app/v1/static')
  ) {
    return next();
  }

  try {
    let getAuthDetails = jwt.verify(
      request.headers.authorization,
      JWT.APP_SECRET_KEY,
      { algorithm: JWT.ALGORITHM }
    );

    let getUserAuthDetails = await AuthModal.findUserToken({
      user_id: getAuthDetails.user_id,
      token: request.headers.authorization,
    });

    if (getUserAuthDetails) {
      request.userId = getUserAuthDetails.user_id;
      next();
    } else {
      return response.handler.unauthorized(API.INVALID_TOKEN);
    }
  } catch (error) {
    switch (error.name) {
      case 'TokenExpiredError':
        return response.handler.unauthorized(API.TOKEN_EXPIRED);

      case 'JsonWebTokenError':
        if (error.message === 'invalid algorithm') {
          break;
        }
        return response.handler.unauthorized(API.INVALID_TOKEN);
      default:
        console.log(error);
        response.handler.serverError(error);
        break;
    }
  }
};
