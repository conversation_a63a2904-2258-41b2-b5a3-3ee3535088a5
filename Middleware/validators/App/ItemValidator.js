const { body, query } = require('express-validator');
const { headerValidator } = require('../CommonValidator');

exports.createItem = [
  ...headerValidator,

  body('buttonId')
    .trim()
    .notEmpty()
    .withMessage('Button ID is required')
    .isInt()
    .withMessage('Button ID must be an integer'),

  body('buttonType')
    .trim()
    .notEmpty()
    .withMessage('Button type is required')
    .isInt({ min: 1, max: 3 })
    .withMessage('Button type must be 1, 2, or 3')
    .customSanitizer((value) => {
      const typeMap = {
        1: 'COUNT',
        2: 'DURATION',
        3: 'VALUE',
      };
      return typeMap[value];
    })
    .custom((value) => {
      if (!value) {
        throw new Error('Invalid button type');
      }
      return true;
    }),

  // Common optional fields for all button types
  body('displayTime')
    .optional()
    .trim()
    .isString()
    .withMessage('Display time must be a string'),

  body('displayDate')
    .optional()
    .trim()
    .isString()
    .withMessage('Display date must be a string'),

  body('displayMonthYear')
    .optional()
    .trim()
    .isString()
    .withMessage('Display month year must be a string'),

  // Type-specific validation
  body().custom((value, { req }) => {
    const buttonType = req.body.buttonType;
    const commonFields = [
      'buttonId',
      'buttonType',
      'displayTime',
      'displayDate',
      'displayMonthYear',
    ];
    const allowedFields = [...commonFields];

    if (buttonType === 'COUNT') {
      allowedFields.push('countIncrement', 'countTimeStamp');
    } else if (buttonType === 'DURATION') {
      allowedFields.push(
        'durationStartTimeStamp',
        'durationStopTimeStamp',
        'durationTimeMs'
      );
    } else if (buttonType === 'VALUE') {
      allowedFields.push(
        'itemName',
        'itemValue',
        'valueUnit',
        'valueTimeStamp'
      );
    }

    // Throw error for fields not allowed for the current button type
    const invalidFields = Object.keys(req.body).filter(
      (key) => !allowedFields.includes(key)
    );
    if (invalidFields.length > 0) {
      throw new Error(
        `Invalid fields for ${buttonType} button type: ${invalidFields.join(
          ', '
        )}`
      );
    }

    return true;
  }),

  // Keep the existing field validations
  body('countIncrement')
    .if(body('buttonType').equals('COUNT'))
    .notEmpty()
    .withMessage('Count increment value is required for COUNT button type')
    .trim()
    .isString()
    .withMessage('Count increment value must be a string'),

  body('countTimeStamp')
    .if(body('buttonType').equals('COUNT'))
    .notEmpty()
    .withMessage('Count timestamp is required for COUNT button type'),

  body('durationStartTimeStamp')
    .if(body('buttonType').equals('DURATION'))
    .notEmpty()
    .withMessage(
      'Duration start timestamp is required for DURATION button type'
    ),

  body('durationStopTimeStamp')
    .if(body('buttonType').equals('DURATION'))
    .notEmpty()
    .withMessage(
      'Duration stop timestamp is required for DURATION button type'
    ),

  body('durationTimeMs')
    .if(body('buttonType').equals('DURATION'))
    .notEmpty()
    .withMessage('Duration time is required for DURATION button type')
    .isInt({ min: 0 })
    .withMessage('Duration time must be a positive integer'),

  body('itemName')
    .if(body('buttonType').equals('VALUE'))
    .optional({ nullable: true })
    .trim()
    .isString()
    .withMessage('Item name must be a string'),

  body('itemValue')
    .if(body('buttonType').equals('VALUE'))
    .trim()
    .notEmpty()
    .withMessage('Item value is required for VALUE button type')
    .isString()
    .withMessage('Item value must be a string'),

  body('valueUnit')
    .if(body('buttonType').equals('VALUE'))
    .optional({ nullable: true })
    .trim()
    .isString()
    .withMessage('Value unit must be a string'),

  body('valueTimeStamp')
    .if(body('buttonType').equals('VALUE'))
    .notEmpty()
    .withMessage('Value timestamp is required for VALUE button type'),
];

exports.updateItem = [
  ...headerValidator,

  body('itemId')
    .trim()
    .notEmpty()
    .withMessage('Item ID is required')
    .isInt()
    .withMessage('Item ID must be an integer'),

  body('buttonId')
    .trim()
    .notEmpty()
    .withMessage('Button Id is required')
    .isInt()
    .withMessage('Button ID must be an integer'),

  body('buttonType')
    .trim()
    .notEmpty()
    .withMessage('Button type is required')
    .isInt({ min: 1, max: 3 })
    .withMessage('Button type must be 1, 2, or 3')
    .customSanitizer((value) => {
      const typeMap = {
        1: 'COUNT',
        2: 'DURATION',
        3: 'VALUE',
      };
      return typeMap[value];
    })
    .custom((value) => {
      if (!value) {
        throw new Error('Invalid button type');
      }
      return true;
    }),

  body('displayTime')
    .optional()
    .trim()
    .isString()
    .withMessage('Display time must be a string'),

  body('displayDate')
    .optional()
    .trim()
    .isString()
    .withMessage('Display date must be a string'),

  body('displayMonthYear')
    .optional()
    .trim()
    .isString()
    .withMessage('Display month year must be a string'),

  body().custom((value, { req }) => {
    const buttonType = req.body.buttonType;
    const commonFields = [
      'itemId',
      'buttonId',
      'buttonType',
      'displayTime',
      'displayDate',
      'displayMonthYear',
    ];
    const allowedFields = [...commonFields];

    if (buttonType === 'COUNT') {
      allowedFields.push('countIncrement', 'countTimeStamp');
    } else if (buttonType === 'DURATION') {
      allowedFields.push('durationStartTimeStamp', 'durationTimeMs');
    } else if (buttonType === 'VALUE') {
      allowedFields.push('itemValue', 'valueTimeStamp');
    }

    // Throw error for fields not allowed for the current button type
    const invalidFields = Object.keys(req.body).filter(
      (key) => !allowedFields.includes(key)
    );
    if (invalidFields.length > 0) {
      throw new Error(
        `Invalid fields for ${buttonType} button type: ${invalidFields.join(
          ', '
        )}`
      );
    }

    return true;
  }),

  body('countIncrement')
    .if(body('buttonType').equals('COUNT'))
    .notEmpty()
    .withMessage('Count increment value is required for COUNT button type')
    .trim()
    .isString()
    .withMessage('Count increment value must be a string'),

  body('countTimeStamp')
    .if(body('buttonType').equals('COUNT'))
    .notEmpty()
    .withMessage('Count timestamp is required for COUNT button type'),

  body('durationStartTimeStamp')
    .if(body('buttonType').equals('DURATION'))
    .notEmpty()
    .withMessage(
      'Duration start timestamp is required for DURATION button type'
    ),

  body('durationTimeMs')
    .if(body('buttonType').equals('DURATION'))
    .notEmpty()
    .withMessage('Duration time is required for DURATION button type')
    .isInt({ min: 0 })
    .withMessage('Duration time must be a positive integer'),

  body('itemValue')
    .if(body('buttonType').equals('VALUE'))
    .trim()
    .notEmpty()
    .withMessage('Item value is required for VALUE button type')
    .isString()
    .withMessage('Item value must be a string'),

  body('valueTimeStamp')
    .if(body('buttonType').equals('VALUE'))
    .notEmpty()
    .withMessage('Value timestamp is required for VALUE button type'),
];

exports.deleteItem = [
  ...headerValidator,

  query('itemId')
    .trim()
    .notEmpty()
    .withMessage('Item ID is required')
    .isInt()
    .withMessage('Item ID must be an integer'),
  query('buttonId')
    .trim()
    .notEmpty()
    .withMessage('Button ID is required')
    .isInt()
    .withMessage('Button ID must be an integer'),
];

exports.listItems = [
  ...headerValidator,

  query('buttonId')
    .trim()
    .notEmpty()
    .withMessage('Button ID is required')
    .isInt()
    .withMessage('Button ID must be an integer'),
];
