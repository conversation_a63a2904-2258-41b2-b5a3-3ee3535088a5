const { body, query } = require('express-validator');
const headerValidator = require('../CommonValidator').headerValidator;

exports.getUserProfile = [...headerValidator];

exports.updateUserProfile = [
  ...headerValidator,
  body('name').trim().notEmpty().withMessage('Please provide name.'),
];

exports.deleteUserProfile = [...headerValidator];

exports.changePassword = [
  ...headerValidator,
  body('currentPassword', 'Please provide current password.').trim().notEmpty(),
  body('newPassword', 'Please provide new password.')
    .trim()
    .notEmpty()
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[@#$%^&+=]).{8,}$/, 'i')
    .withMessage(
      'Password should be a minimum of 8 characters and must include one lowercase character, one uppercase character, a number, and a special character.'
    ),
  body('confirmPassword', 'Please provide confirm password.').trim().notEmpty(),
  body().custom((value, { req }) => {
    const newPassword = req.body.newPassword;
    const confirmPassword = req.body.confirmPassword;

    if (newPassword !== confirmPassword) {
      throw new Error('Passwords do not match.');
    }

    return true;
  }),
];

exports.upgradeStatus = [
  ...headerValidator,
  body('price').trim().notEmpty().withMessage('Please provide price.'),
];
