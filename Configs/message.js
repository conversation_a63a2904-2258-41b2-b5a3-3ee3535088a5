exports.API = {
  SUCCESS: 'Success',
  BAD_REQUEST: 'Bad Request',
  CREATED: 'Created',
  UNAUTHORIZED: 'Unauthorized',
  FORBIDDEN: 'Forbidden',
  NOT_FOUND: 'Not Found',
  CONFLICT: 'Conflict',
  PRECONDITION_FAILED: 'Precondition Failed',
  VALIDATION_ERROR: 'Validation Error',
  SERVER_ERROR: 'Server Error',
  NOT_ALLOWED: 'Not Allowed',
  SERVICE_UNAVAILABLE: 'Service Unavailable',
  INVALID_TOKEN: 'Invalid Token!',
  TOKEN_EXPIRED: 'Token Expired!',
  EMAIL_EXISTS: 'Email already exist.',
  PHONE_NUMBER_EXISTS: 'Phone number already exist.',
  REGISTER_SUCCESS:
    'Account registered successfully. Please verify your account.',
  USER_DELETED: 'Account was deleted.',
  USER_UNVERIFIED: 'OTP has been successfully sent.',
  USER_NOT_VERIFIED: 'User not verified!',
  USER_INACTIVE:
    'Your account has been inactivated. Please contact admin to active it.',
  USER_INVALID_CREDENTIALS: 'Invalid credentials!',
  LOGIN_FAILED: 'Invalid email or password!',
  LOGIN_SUCCESS: 'Login successful!',
  LOGIN_INACTIVE:
    'Your account has been inactivated by admin. Please contact admin to active it.',
  INVALID_AUTH_TOKEN: 'Unauthorized',
  PROFILE_SUCCESS: 'Profile has been retrieved successfully!',
  CHANGE_PASSWORD_FAILED:
    'Current password does not match with your existing password.',
  CHANGE_PASSWORD_SUCCESS: 'Your password has been successfully changed.',
  UPDATE_PROFILE_SUCCESS: 'Your profile has been successfully updated.',
  FORGOT_PASSWORD_FAILED: 'Email does not exists.',
  USER_NORMAL_PASSWORD_CHANGE:
    'You can only change password in normal account.',
  USER_DELETE_SUCCESS: 'User deleted successfully.',
  USER_GOOGLE_EXISTS: 'User already exists with google account.',
  USER_NORMAL_EXISTS: 'User already exists using normal login with this email.',
  FORGOT_PASSWORD_SUCCESS: 'Forgot password otp has been successfully sent.',
  EXPIRED_OTP: 'OTP has been expired.',
  INVALID_OTP: 'Invalid OTP.',
  RESET_PASSWORD_SUCCESS: 'Your password has been successfully reset.',
  PASSWORD_NOT_MATCH: 'Password and confirm password does not match.',
  ADMIN_NOT_FOUND: 'Admin not found!',
  USER_NOT_FOUND: 'User not found!',
  SETTING_SUCCESS: 'Settings has been successfully updated.',
  USER_ALREADY_VERIFIED: 'User already verified.',
  USER_VERIFIED_SUCCESS: 'Your account has been successfully verified.',
  RESEND_VERIFICATION_SUCCESS:
    'Resend verification otp has been sent successfully.',
  LOGOUT_SUCCESS: 'Logged out successfully.',
  USERS_LIST_SUCCESS: 'Users list fetched successfully.',
  USER_DETAILS_SUCCESS: 'Users details fetched successfully.',
  USER_ACTIVE_SUCCESS: 'User activated successfully.',
  USER_INACTIVE_SUCCESS: 'User inactivated successfully.',
  USER_ACTIVE_INACTIVE_ERROR:
    'Something went wrong while active/inactive user.Please Try Again!',
  USER_DELETE_SUCCESS: 'User deleted successfully.',
  USER_DELETE_ERROR:
    'Something went wrong while deleting user.Please Try Again!',
  BUTTON_CREATED: 'Button created successfully',
  BUTTON_UPDATED: 'Button updated successfully',
  BUTTON_FETCHED: 'Button fetched successfully',
  BUTTON_DELETED: 'Button deleted successfully',
  BUTTON_TYPE_DO_NOT_MATCH: 'Button type do not match',
  BUTTON_NOT_FOUND: 'Requested Button not found',
  BUTTON_ALREADY_DELETED: 'Button already deleted',
  BUTTON_HAVE_ITEM_HISTORY:
    'Cannot update button type, Button have item history',
  DUPLICATE_SEQUENCE_FOUND: 'Duplicate sequences are not allowed',
  INVALID_SEQUENCE_VALUES: 'Invalid sequence values',
  BUTTON_SEQUENCE_UPDATED: 'Button sequence updated successfully',
  ITEM_NOT_FOUND: 'Item not found',
  ITEM_CREATED: 'Item created successfully',
  ITEM_UPDATED: 'Item updated successfully',
  ITEM_DELETED: 'Item deleted successfully',
  ITEM_ALREADY_DELETED: 'Item already deleted',
  TAG_CREATED: 'Tag created successfully',
  TAG_UPDATED: 'Tag updated successfully',
  TAG_DELETED: 'Tag deleted successfully',
  TAG_TYPE_DO_NOT_MATCH: 'Tag type do not match',
  CANNOT_UPDATE_GPS_TAG: 'Cannot update Location tag',
  TAG_NOT_FOUND: 'Requested Tag not found',
  TAG_ALREADY_DELETED: 'Tag already deleted',
  ALARM_NOT_FOUND: 'Requested Alarm not found',
  ALARM_UPDATED: 'Alarm updated successfully',
  BUTTON_IDEAS_EMPTY: 'Button ideas not found',
  INVALID_MESSAGE_FORMAT: 'Invalid Pub/Sub message format',
  WEBHOOK_PROCESSED_SUCCESSFULLY: 'Webhook processed successfully',
};
