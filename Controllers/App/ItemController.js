const ButtonModel = new (require('../../Models/App/ButtonModel'))();
const ItemModel = new (require('../../Models/App/ItemModel'))();
const { BUTTON_TYPES, STATUS_CODES } = require('../../Configs/constants');
const { API } = require('../../Configs/message');

class ItemController {
  async createItem(req, res) {
    const body = req.body;
    const userId = req.userId;

    try {
      //   const user = await AuthModel.findUser({ id: userId });

      //   if (!user) {
      //     return res.handler.custom(STATUS_CODES.NOT_FOUND, API.USER_NOT_FOUND);
      //   }

      // Get the button to determine its type
      const button = await ButtonModel.findButton({
        id: body.buttonId,
        user_id: userId,
      });

      if (!button) {
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.BUTTON_NOT_FOUND);
      }

      if (button.button_type !== body.buttonType) {
        return res.handler.custom(
          STATUS_CODES.BAD_REQUEST,
          API.BUTTON_TYPE_DO_NOT_MATCH
        );
      }

      // Base payload with common fields
      const payload = {
        button_id: body.buttonId,
        user_id: userId,
        display_time: body.displayTime,
        display_date: body.displayDate,
        display_month_year: body.displayMonthYear,
      };

      // Add fields based on button type
      if (body.buttonType === BUTTON_TYPES.COUNT) {
        payload.count_value = body.countIncrement;
        payload.count_time_stamp = body.countTimeStamp;
      } else if (body.buttonType === BUTTON_TYPES.DURATION) {
        payload.duration_start_time_stamp = body.durationStartTimeStamp;
        payload.duration_stop_time_stamp = body.durationStopTimeStamp;
        payload.duration_time_ms = body.durationTimeMs;
      } else if (body.buttonType === BUTTON_TYPES.VALUE) {
        payload.item_name = body.itemName;
        payload.item_value = body.itemValue;
        payload.value_unit = body.valueUnit;
        payload.value_time_stamp = body.valueTimeStamp;
      }
      const item = await ItemModel.createItem(payload);

      const itemData = ItemModel.itemFormatInstance({
        ...item.dataValues,
        button_type: button.button_type,
      });

      return res.handler.custom(
        STATUS_CODES.SUCCESS,
        API.ITEM_CREATED,
        itemData
      );
    } catch (error) {
      return res.handler.serverError(error);
    }
  }

  async updateItem(req, res) {
    const body = req.body;
    const userId = req.userId;

    try {
      const button = await ButtonModel.findButton({
        id: body.buttonId,
        user_id: userId,
      });

      if (!button) {
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.BUTTON_NOT_FOUND);
      }

      if (button.button_type !== body.buttonType) {
        return res.handler.custom(
          STATUS_CODES.BAD_REQUEST,
          API.BUTTON_TYPE_DO_NOT_MATCH
        );
      }

      const item = await ItemModel.findItem({
        id: body.itemId,
        user_id: userId,
        button_id: body.buttonId,
      });

      if (!item) {
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.ITEM_NOT_FOUND);
      }

      const payload = {
        display_time: body.displayTime,
        display_date: body.displayDate,
        display_month_year: body.displayMonthYear,
      };

      if (body.buttonType === BUTTON_TYPES.COUNT) {
        payload.count_value = body.countIncrement;
        payload.count_time_stamp = body.countTimeStamp;
      } else if (body.buttonType === BUTTON_TYPES.DURATION) {
        payload.duration_start_time_stamp = body.durationStartTimeStamp;
        payload.duration_time_ms = body.durationTimeMs;
      } else if (body.buttonType === BUTTON_TYPES.VALUE) {
        payload.item_value = body.itemValue;
        payload.value_time_stamp = body.valueTimeStamp;
      }

      await ItemModel.updateItem(
        {
          id: body.itemId,
          user_id: userId,
          button_id: body.buttonId,
        },
        payload
      );

      const itemData = await ItemModel.findItem({
        id: body.itemId,
        user_id: userId,
      });

      const itemDataFormatted = ItemModel.itemFormatInstance({
        ...itemData.dataValues,
        button_type: button.button_type,
      });

      return res.handler.custom(
        STATUS_CODES.SUCCESS,
        API.ITEM_UPDATED,
        itemDataFormatted
      );
    } catch (error) {
      return res.handler.serverError(error);
    }
  }

  async deleteItem(req, res) {
    const query = req.query;
    const userId = req.userId;

    try {
      const item = await ItemModel.findItem({
        id: query.itemId,
        button_id: query.buttonId,
        user_id: userId,
      });

      if (!item) {
        return res.handler.custom(STATUS_CODES.NOT_FOUND, API.ITEM_NOT_FOUND);
      }

      if (item.is_deleted) {
        return res.handler.custom(
          STATUS_CODES.BAD_REQUEST,
          API.ITEM_ALREADY_DELETED
        );
      }

      await ItemModel.updateItem(
        {
          id: query.itemId,
          button_id: query.buttonId,
          user_id: userId,
        },
        { is_deleted: true }
      );

      return res.handler.custom(STATUS_CODES.SUCCESS, API.ITEM_DELETED);
    } catch (error) {
      return res.handler.serverError(error);
    }
  }

  async listItems(req, res) {
    const query = req.query;
    const userId = req.userId;

    try {
      const items = await ItemModel.findItems(
        {
          user_id: userId,
          button_id: query.buttonId,
        },
        false,
        {
          order: [['created_at', 'DESC']],
        }
      );

      const itemData = items.map((item) => ItemModel.itemFormatInstance(item));

      return res.handler.custom(
        STATUS_CODES.SUCCESS,
        API.ITEM_FETCHED,
        itemData
      );
    } catch (error) {
      return res.handler.serverError(error);
    }
  }
}

module.exports = ItemController;
