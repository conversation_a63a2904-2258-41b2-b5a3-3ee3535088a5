const { Op } = require('sequelize');
const {
  BUTTON_TYPES,
  OPERATION_TYPES,
  BUTTON_TYPES_NUMBERS,
  TAG_TYPES,
} = require('../../Configs/constants');
const { API } = require('../../Configs/message');

const ButtonModel = new (require('../../Models/App/ButtonModel'))();
const ItemModel = new (require('../../Models/App/ItemModel'))();
const TagModel = new (require('../../Models/App/TagModel'))();
const AlarmModel = new (require('../../Models/App/AlarmModel'))();

class SyncController {
  #buttonIdMap = new Map();
  #itemIdMap = new Map();
  #tagIdMap = new Map();
  #alarmIdMap = new Map();

  #resetMaps() {
    this.#buttonIdMap.clear();
    this.#itemIdMap.clear();
    this.#tagIdMap.clear();
    this.#alarmIdMap.clear();
  }

  // Helper method to check if request is aborted
  #checkRequestAborted(req) {
    if (req.aborted || req.socket?.destroyed) {
      console.log('req.aborted: ', req.aborted);
      console.log('req.destroyed: ', req.destroyed);
      console.log('req.socket?.destroyed: ', req.socket?.destroyed);
      console.log('Client disconnected, aborting operations...');
      const error = new Error('Request was aborted by client');
      error.code = 'REQUEST_ABORTED';
      error.message = 'Request was aborted by client';
      throw error;
    }
  }

  // Create AbortController for internal operations
  #createAbortController(req) {
    const controller = new AbortController();

    // Listen for client disconnect
    const onAbort = () => {
      console.log('Client disconnected, aborting operations...');
      controller.abort();
    };

    req.on('aborted', onAbort);
    req.on('close', onAbort);
    req.socket?.on('close', onAbort);

    // Cleanup listeners when done
    const cleanup = () => {
      req.removeListener('aborted', onAbort);
      req.removeListener('close', onAbort);
      req.socket?.removeListener('close', onAbort);
    };

    console.countReset('operation');
    console.timeEnd('batchSync');

    return { controller, cleanup };
  }

  async batchSync(req, res) {
    this.#resetMaps();
    // const syncIdMap = new Map();

    const { operations } = req.body;
    const userId = req.userId;
    const responseData = [];

    const { controller: abortController, cleanup } =
      this.#createAbortController(req);

    console.log('operations:: ', operations.length);

    const transaction = await ButtonModel.getTransaction();

    try {
      console.time('batchSync');
      for (const operation of operations) {
        // Check if request was aborted before processing each operation
        this.#checkRequestAborted(req);

        // Check AbortController signal
        if (abortController.signal.aborted) {
          throw new Error('Request was aborted by client');
          break;
        }

        // console.log(`Processing operation ${i + 1}/${operations.length}`);

        console.count('operation');

        await this.operationHandler(operation, userId, transaction);

        const buttonId = this.#buttonIdMap.get(operation.localButtonId) || null;
        responseData.push({
          operationType: operation.operationType,
          syncId: operation.syncId,
          btnId: buttonId,
          alarmId: this.#alarmIdMap.get(buttonId) || null,
          itemId: this.#itemIdMap.get(operation.localItemId) || null,
          tagId: this.#tagIdMap.get(operation.localTagId) || null,
          localButtonId: operation.localButtonId || null,
          localItemId: operation.localItemId || null,
          localTagId: operation.localTagId || null,
        });
      }
      await transaction.commit();
      console.timeEnd('batchSync');
      return res.handler.success(null, responseData);
    } catch (error) {
      console.log('Error in batch sync:: ', error);
      await transaction.rollback();
      return res.handler.serverError(error);
    }
  }

  async operationHandler(operation, userId, transaction) {
    const {
      operationType,
      payload,
      syncId,
      localButtonId,
      localItemId,
      localTagId,
    } = operation;
    try {
      const opData = {
        ...payload,
        userId,
        localButtonId,
        localItemId,
        localTagId,
      };

      switch (operationType) {
        case OPERATION_TYPES.ADD_BUTTON:
          await this.createButtonSync(opData, transaction);
          break;

        case OPERATION_TYPES.EDIT_BUTTON:
          await this.updateButtonSync(opData, transaction);
          break;

        case OPERATION_TYPES.DELETE_BUTTON:
          await this.deleteButtonSync(opData, transaction);
          break;

        case OPERATION_TYPES.CREATE_COUNT_BUTTON_ITEM:
        case OPERATION_TYPES.CREATE_DURATION_BUTTON_ITEM:
        case OPERATION_TYPES.CREATE_VALUE_BUTTON_ITEM:
          await this.createItemSync(opData, transaction);
          break;

        case OPERATION_TYPES.EDIT_COUNT_BUTTON_ITEM:
        case OPERATION_TYPES.EDIT_DURATION_BUTTON_ITEM:
        case OPERATION_TYPES.EDIT_VALUE_BUTTON_ITEM:
          await this.updateItemSync(opData, transaction);
          break;

        case OPERATION_TYPES.DELETE_COUNT_BUTTON_ITEM:
        case OPERATION_TYPES.DELETE_DURATION_BUTTON_ITEM:
        case OPERATION_TYPES.DELETE_VALUE_BUTTON_ITEM:
          await this.deleteItemSync(opData, transaction);
          break;

        case OPERATION_TYPES.CREATE_ITEM_TAG:
          await this.createTagSync(opData, transaction);
          break;

        case OPERATION_TYPES.EDIT_ITEM_TAG:
          await this.updateTagSync(opData, transaction);
          break;

        case OPERATION_TYPES.DELETE_ITEM_TAG:
          await this.deleteTagSync(opData, transaction);
          break;

        case OPERATION_TYPES.UPDATE_BUTTON_ALARM:
          await this.updateAlarmSync(opData, transaction);
          break;

        case OPERATION_TYPES.CHANGE_BUTTON_SEQUENCE:
          await this.changeButtonSequenceSync(opData, transaction);
          break;

        default:
          throw new Error(`Invalid operationType: ${operationType}`);
      }
    } catch (error) {
      throw error;
    }
  }

  async createButtonSync(data, transaction) {
    const buttonType = BUTTON_TYPES_NUMBERS[data.buttonType];

    if (!buttonType) {
      throw new Error(API.BUTTON_TYPE_DO_NOT_MATCH);
    }

    const payload = {
      user_id: data.userId,
      button_name: data.buttonName,
      button_color: data.buttonColor,
      button_type: buttonType,
      button_shape: data.buttonShape,
      button_summery_calculation: JSON.stringify(data.buttonSummeryCalculation),
      button_sequence: data.buttonSequence,
    };

    if (buttonType === BUTTON_TYPES.COUNT) {
      payload.count_inc = Number(data.countInc);
    } else if (buttonType === BUTTON_TYPES.VALUE) {
      payload.value_unit = data.valueUnit || null;
      payload.value_unit_description = data.valueUnitDescription || null;
      payload.value_item_name = data.valueItemName;
    }

    if (data.alarmTag !== undefined) payload.alarm_tag = data.alarmTag;
    if (data.textNoteTag !== undefined)
      payload.text_note_tag = data.textNoteTag;
    if (data.locationTag !== undefined) payload.location_tag = data.locationTag;

    const button = await ButtonModel.createButton(payload, { transaction });

    if (!button) {
      throw new Error('Button not created');
    }

    const alarmPayload = {
      button_id: button.id,
      user_id: data.userId,
      alarm_time: null,
      snooze_time: null,
      repeat_daily: false,
      is_ring_after: false,
      is_ring_after_always: false,
      ring_after_time_stamp: null,
      ring_after_time_ms: null,
      is_active: false,
    };

    const alarm = await AlarmModel.createAlarm(alarmPayload, { transaction });

    if (!alarm) {
      throw new Error('Alarm not created');
    }

    this.#alarmIdMap?.set(button.id, alarm.id);
    this.#buttonIdMap?.set(data.localButtonId, button.id);
  }

  async updateButtonSync(data, transaction) {
    // Check if either buttonId or localButtonId exists
    if (!data.buttonId && !data.localButtonId) {
      throw new Error(
        'Either buttonId or localButtonId is required for button update'
      );
    }

    const buttonId = data.buttonId || this.#buttonIdMap.get(data.localButtonId);
    if (!buttonId) throw new Error(API.BUTTON_NOT_FOUND);

    const buttonType = BUTTON_TYPES_NUMBERS[data.buttonType];

    if (!buttonType) {
      throw new Error(API.BUTTON_TYPE_DO_NOT_MATCH);
    }

    // Get the existing button
    const button = await ButtonModel.findButton(
      {
        id: buttonId,
        user_id: data.userId,
      },
      false,
      { transaction }
    );
    if (!button) {
      throw new Error(API.BUTTON_NOT_FOUND);
    }

    const payload = {
      button_name: data.buttonName,
      button_color: data.buttonColor,
      button_shape: data.buttonShape,
      button_summery_calculation: JSON.stringify(data.buttonSummeryCalculation),
      button_sequence: data.buttonSequence,
    };

    // Check if button type is changed
    if (buttonType !== undefined && buttonType !== button.button_type) {
      // Check if items exist for this button
      const items = await ItemModel.countItems({ button_id: buttonId }, false, {
        transaction,
      });
      if (items > 0) {
        throw new Error(API.BUTTON_HAVE_ITEM_HISTORY);
      }

      payload.button_type = buttonType;

      if (buttonType === BUTTON_TYPES.COUNT) {
        payload.value_unit = null;
        payload.value_unit_description = null;
        payload.value_item_name = null;
      } else if (buttonType === BUTTON_TYPES.VALUE) {
        payload.count_inc = null;
      } else if (buttonType === BUTTON_TYPES.DURATION) {
        payload.count_inc = null;
        payload.value_unit = null;
        payload.value_unit_description = null;
        payload.value_item_name = null;
      }
    }

    // Add type-specific fields based on the current button type
    if (button.button_type === BUTTON_TYPES.COUNT) {
      payload.count_inc = Number(data.countInc);
    } else if (button.button_type === BUTTON_TYPES.VALUE) {
      payload.value_unit = data.valueUnit || null;
      payload.value_unit_description = data.valueUnitDescription || null;
      payload.value_item_name = data.valueItemName;
    }

    // Add optional tag fields
    if (data.alarmTag !== undefined) payload.alarm_tag = data.alarmTag;
    if (data.textNoteTag !== undefined)
      payload.text_note_tag = data.textNoteTag;
    if (data.locationTag !== undefined) payload.location_tag = data.locationTag;

    // Update the button
    await ButtonModel.updateButton(
      { id: buttonId, user_id: data.userId },
      payload,
      { transaction }
    );

    this.#alarmIdMap?.set(button.id, button.id);
    this.#buttonIdMap?.set(data.localButtonId, button.id);
  }

  async deleteButtonSync(data, transaction) {
    const buttonId = data.buttonId || this.#buttonIdMap.get(data.localButtonId);
    if (!buttonId) throw new Error(API.BUTTON_NOT_FOUND);

    const button = await ButtonModel.findButton(
      {
        id: buttonId,
        user_id: data.userId,
      },
      false,
      { transaction }
    );
    if (!button) {
      throw new Error(API.BUTTON_NOT_FOUND);
    }

    if (button.is_deleted) {
      throw new Error(API.BUTTON_ALREADY_DELETED);
    }

    await ButtonModel.updateButton(
      { id: buttonId, user_id: data.userId },
      { is_deleted: true },
      { transaction }
    );

    this.#alarmIdMap?.set(button.id, button.id);
    this.#buttonIdMap?.set(data.localButtonId, button.id);
  }

  async createItemSync(data, transaction) {
    const buttonId = data.buttonId || this.#buttonIdMap.get(data.localButtonId);
    if (!buttonId) throw new Error(API.BUTTON_NOT_FOUND);

    const button = await ButtonModel.findButton(
      {
        id: buttonId,
        user_id: data.userId,
      },
      false,
      { transaction }
    );

    if (!button) {
      console.log('buttonId:: ', buttonId);
      console.log('data:: ', data);
      console.log('button map:: ', this.#buttonIdMap);
      throw new Error(API.BUTTON_NOT_FOUND);
    }

    const buttonType = BUTTON_TYPES_NUMBERS[data.buttonType];

    if (!buttonType || buttonType !== button.button_type) {
      throw new Error(API.BUTTON_TYPE_DO_NOT_MATCH);
    }

    const payload = {
      button_id: buttonId,
      user_id: data.userId,
      display_time: data.displayTime,
      display_date: data.displayDate,
      display_month_year: data.displayMonthYear,
    };

    if (buttonType === BUTTON_TYPES.COUNT) {
      payload.count_value = data.countIncrement;
      payload.count_time_stamp = data.countTimeStamp;
    } else if (buttonType === BUTTON_TYPES.DURATION) {
      payload.duration_start_time_stamp = data.durationStartTimeStamp;
      payload.duration_stop_time_stamp = data.durationStopTimeStamp;
      payload.duration_time_ms = data.durationTimeMs;
    } else if (buttonType === BUTTON_TYPES.VALUE) {
      payload.item_name = data.itemName;
      payload.item_value = data.itemValue;
      payload.value_unit = data.valueUnit;
      payload.value_time_stamp = data.valueTimeStamp;
    }

    const item = await ItemModel.createItem(payload, { transaction });

    if (!item) {
      throw new Error('Item not created');
    }

    this.#itemIdMap?.set(data.localItemId, item.id);
    this.#buttonIdMap?.set(data.localButtonId, buttonId);
  }

  async updateItemSync(data, transaction) {
    const buttonId = data.buttonId || this.#buttonIdMap.get(data.localButtonId);
    const itemId = data.itemId || this.#itemIdMap.get(data.localItemId);

    if (!buttonId) throw new Error(API.BUTTON_NOT_FOUND);
    if (!itemId) throw new Error(API.ITEM_NOT_FOUND);

    const button = await ButtonModel.findButton(
      {
        id: buttonId,
        user_id: data.userId,
      },
      false,
      { transaction }
    );

    if (!button) {
      throw new Error(API.BUTTON_NOT_FOUND);
    }

    const buttonType = BUTTON_TYPES_NUMBERS[data.buttonType];

    if (!buttonType || buttonType !== button.button_type) {
      throw new Error(API.BUTTON_TYPE_DO_NOT_MATCH);
    }

    const item = await ItemModel.findItem(
      {
        id: itemId,
        user_id: data.userId,
        button_id: buttonId,
      },
      false,
      { transaction }
    );

    if (!item) {
      throw new Error(API.ITEM_NOT_FOUND);
    }

    const payload = {
      display_time: data.displayTime,
      display_date: data.displayDate,
      display_month_year: data.displayMonthYear,
    };

    if (buttonType === BUTTON_TYPES.COUNT) {
      payload.count_value = data.countIncrement;
      payload.count_time_stamp = data.countTimeStamp;
    } else if (buttonType === BUTTON_TYPES.DURATION) {
      payload.duration_start_time_stamp = data.durationStartTimeStamp;
      payload.duration_time_ms = data.durationTimeMs;
    } else if (buttonType === BUTTON_TYPES.VALUE) {
      payload.item_value = data.itemValue;
      payload.value_time_stamp = data.valueTimeStamp;
    }

    await ItemModel.updateItem(
      {
        id: itemId,
        user_id: data.userId,
        button_id: buttonId,
      },
      payload,
      { transaction }
    );

    this.#itemIdMap?.set(data.localItemId, item.id);
    this.#buttonIdMap?.set(data.localButtonId, buttonId);
  }

  async deleteItemSync(data, transaction) {
    const buttonId = data.buttonId || this.#buttonIdMap.get(data.localButtonId);
    const itemId = data.itemId || this.#itemIdMap.get(data.localItemId);

    if (!buttonId) throw new Error(API.BUTTON_NOT_FOUND);
    if (!itemId) throw new Error(API.ITEM_NOT_FOUND);

    const item = await ItemModel.findItem(
      {
        id: itemId,
        button_id: buttonId,
        user_id: data.userId,
      },
      false,
      { transaction }
    );

    if (!item) {
      throw new Error(API.ITEM_NOT_FOUND);
    }

    if (item.is_deleted) {
      throw new Error(API.ITEM_ALREADY_DELETED);
    }

    await ItemModel.updateItem(
      {
        id: itemId,
        button_id: buttonId,
        user_id: data.userId,
      },
      { is_deleted: true },
      { transaction }
    );

    this.#itemIdMap?.set(data.localItemId, item.id);
    this.#buttonIdMap?.set(data.localButtonId, buttonId);
  }

  async createTagSync(data, transaction) {
    const buttonId = data.buttonId || this.#buttonIdMap.get(data.localButtonId);
    const itemId = data.itemId || this.#itemIdMap.get(data.localItemId);

    if (!buttonId) throw new Error(API.BUTTON_NOT_FOUND);
    if (!itemId) throw new Error(API.ITEM_NOT_FOUND);

    // Verify item exists
    const item = await ItemModel.findItem(
      {
        id: itemId,
        button_id: buttonId,
      },
      false,
      { transaction }
    );
    if (!item) {
      throw new Error(API.ITEM_NOT_FOUND);
    }

    const payload = {
      item_id: itemId,
      button_id: buttonId,
      tag_type: data.tagType,
      tag_title: data.tagTitle,
      tag_value: data.tagValue,
      tag_time_stamp: data.tagTimeStamp,
    };

    const tag = await TagModel.createTag(payload, { transaction });

    if (!tag) {
      throw new Error('Tag not created');
    }

    this.#tagIdMap?.set(data.localTagId, tag.id);
    this.#itemIdMap?.set(data.localItemId, itemId);
    this.#buttonIdMap?.set(data.localButtonId, buttonId);
  }

  async updateTagSync(data, transaction) {
    const buttonId = data.buttonId || this.#buttonIdMap.get(data.localButtonId);
    const itemId = data.itemId || this.#itemIdMap.get(data.localItemId);
    const tagId = data.tagId || this.#tagIdMap.get(data.localTagId);

    if (!buttonId) throw new Error(API.BUTTON_NOT_FOUND);
    if (!itemId) throw new Error(API.ITEM_NOT_FOUND);
    if (!tagId) throw new Error(API.TAG_NOT_FOUND);

    // Verify item exists
    const item = await ItemModel.findItem(
      {
        id: itemId,
        button_id: buttonId,
      },
      false,
      { transaction }
    );
    if (!item) {
      throw new Error(API.ITEM_NOT_FOUND);
    }

    // Find and verify tag
    const tag = await TagModel.findTag(
      {
        id: tagId,
        button_id: buttonId,
        item_id: itemId,
      },
      false,
      { transaction }
    );

    if (!tag) {
      throw new Error(API.TAG_NOT_FOUND);
    }

    // Check if tag type matches
    if (tag.tag_type !== data.tagType) {
      throw new Error(API.TAG_TYPE_DO_NOT_MATCH);
    }

    // Check if it's a GPS tag (which can't be updated)
    if (tag.tag_type === TAG_TYPES.GPS) {
      throw new Error(API.CANNOT_UPDATE_GPS_TAG);
    }

    const payload = {
      tag_title: data.tagTitle,
      tag_value: data.tagValue,
      tag_time_stamp: data.tagTimeStamp,
    };

    await TagModel.updateTag({ id: tagId }, payload, { transaction });

    this.#tagIdMap?.set(data.localTagId, tag.id);
    this.#itemIdMap?.set(data.localItemId, itemId);
    this.#buttonIdMap?.set(data.localButtonId, buttonId);
  }

  async deleteTagSync(data, transaction) {
    const buttonId = data.buttonId || this.#buttonIdMap.get(data.localButtonId);
    const itemId = data.itemId || this.#itemIdMap.get(data.localItemId);
    const tagId = data.tagId || this.#tagIdMap.get(data.localTagId);

    if (!buttonId) throw new Error(API.BUTTON_NOT_FOUND);
    if (!itemId) throw new Error(API.ITEM_NOT_FOUND);
    if (!tagId) throw new Error(API.TAG_NOT_FOUND);

    // Find tag to verify it exists
    const tag = await TagModel.findTag(
      {
        id: tagId,
        button_id: buttonId,
        item_id: itemId,
      },
      false,
      { transaction }
    );

    if (!tag) {
      throw new Error(API.TAG_NOT_FOUND);
    }

    if (tag.is_deleted) {
      throw new Error(API.TAG_ALREADY_DELETED);
    }

    // Mark tag as deleted
    await TagModel.updateTag(
      { id: tagId },
      { is_deleted: true },
      { transaction }
    );

    this.#tagIdMap?.set(data.localTagId, tag.id);
    this.#itemIdMap?.set(data.localItemId, itemId);
    this.#buttonIdMap?.set(data.localButtonId, buttonId);
  }

  async updateAlarmSync(data, transaction) {
    const buttonId = data.buttonId || this.#buttonIdMap.get(data.localButtonId);
    if (!buttonId) throw new Error(API.BUTTON_NOT_FOUND);

    const alarmId = data.alarmId || this.#alarmIdMap.get(buttonId);
    if (!alarmId) throw new Error(API.ALARM_NOT_FOUND);

    const alarm = await AlarmModel.findAlarm(
      {
        id: alarmId,
        button_id: buttonId,
      },
      { transaction }
    );

    if (!alarm) {
      throw new Error(API.ALARM_NOT_FOUND);
    }

    const payload = {
      alarm_time: data.alarmTime,
      snooze_time: data.snoozeTime,
      repeat_daily: data.repeatDaily,
      is_ring_after: data.isRingAfter,
      is_ring_after_always: data.isRingAfterAlways,
      ring_after_time_stamp: data.ringAfterTimeStamp,
      ring_after_time_ms: data.ringAfterTimeMs,
      is_active: data.isActive,
    };

    await AlarmModel.updateAlarm(
      { id: alarmId, button_id: buttonId, user_id: data.userId },
      payload,
      { transaction }
    );

    this.#alarmIdMap?.set(buttonId, alarm.id);
    this.#buttonIdMap?.set(data.localButtonId, buttonId);
  }

  async changeButtonSequenceSync(data, transaction) {
    try {
      const { userId, buttonSequence } = data;

      const buttonIds = buttonSequence.map((sequence) => {
        const buttonId =
          sequence.buttonId || this.#buttonIdMap.get(sequence.localButtonId);

        if (!buttonId) {
          throw new Error(API.BUTTON_NOT_FOUND);
        }

        return buttonId;
      });

      const buttonsCount = await ButtonModel.countButtons(
        {
          user_id: userId,
          id: {
            [Op.in]: buttonIds,
          },
        },
        false,
        { transaction }
      );

      if (!buttonsCount || buttonsCount !== buttonSequence.length) {
        throw new Error(API.BUTTON_NOT_FOUND);
      }

      const sequences = buttonSequence.map((b) => b.sequence);
      if (new Set(sequences).size !== sequences.length) {
        throw new Error(API.DUPLICATE_SEQUENCE_FOUND);
      }
      const updatePromises = buttonSequence.map((sequence) => {
        const buttonId =
          sequence.buttonId || this.#buttonIdMap.get(sequence.localButtonId);
        return ButtonModel.updateButton(
          {
            id: buttonId,
            user_id: userId,
          },
          { button_sequence: sequence.sequence },
          { transaction }
        );
      });

      await Promise.all(updatePromises);
    } catch (error) {
      throw error;
    }
  }
}

module.exports = SyncController;
