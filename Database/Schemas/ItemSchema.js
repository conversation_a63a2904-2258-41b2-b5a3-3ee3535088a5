module.exports = (sequelize, DataTypes) => {
  const ItemSchema = sequelize.define(
    'Item',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      button_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'buttons',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      count_value: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      count_time_stamp: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      duration_start_time_stamp: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      duration_stop_time_stamp: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      duration_time_ms: {
        type: DataTypes.BIGINT.UNSIGNED,
        allowNull: true,
      },
      item_name: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      item_value: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      value_unit: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      value_time_stamp: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      display_time: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      display_date: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      display_month_year: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
    },
    {
      tableName: 'button_items',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      defaultScope: {
        where: {
          is_deleted: false,
        },
      },
    }
  );

  ItemSchema.associate = (models) => {
    ItemSchema.belongsTo(models.Button, {
      as: 'button',
      foreignKey: 'button_id',
    });
    ItemSchema.belongsTo(models.User, { foreignKey: 'user_id' });
    ItemSchema.hasMany(models.Tag, { foreignKey: 'item_id', as: 'tags' });
  };

  return ItemSchema;
};
