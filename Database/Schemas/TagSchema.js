const { TAG_TYPES } = require('../../Configs/constants');

module.exports = (sequelize, DataTypes) => {
  const TagSchema = sequelize.define(
    'Tag',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      item_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'button_items',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      button_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'buttons',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      tag_title: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      tag_type: {
        type: DataTypes.ENUM([Object.values(TAG_TYPES)]),
        allowNull: true,
      },
      tag_value: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      tag_time_stamp: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      is_deleted: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
    },
    {
      tableName: 'item_tags',
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    }
  );

  TagSchema.associate = (models) => {
    TagSchema.belongsTo(models.Item, {
      foreignKey: 'item_id',
      as: 'item',
    });
    TagSchema.belongsTo(models.Button, {
      foreignKey: 'button_id',
      as: 'button',
    });
  };

  return TagSchema;
};
