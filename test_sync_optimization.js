/**
 * Test Suite for Optimized Sync Endpoint
 * 
 * This file demonstrates how to test the optimized sync endpoint
 * with various scenarios including bulk operations, error handling,
 * and performance monitoring.
 */

const request = require('supertest');
const app = require('../app');

describe('Optimized Sync Endpoint', () => {
  let authToken;
  let userId;

  beforeAll(async () => {
    // Setup test user and authentication
    // This would typically involve creating a test user and getting an auth token
    authToken = 'test-auth-token';
    userId = 1;
  });

  describe('Bulk Button Operations', () => {
    test('should handle bulk button creation efficiently', async () => {
      const operations = [];
      
      // Create 100 button operations for bulk testing
      for (let i = 1; i <= 100; i++) {
        operations.push({
          operationType: 'ADD_BUTTON',
          syncId: i,
          localButtonId: i,
          payload: {
            buttonName: `Test Button ${i}`,
            buttonColor: '#FF0000',
            buttonType: 1, // COUNT
            buttonShape: 'CIRCLE',
            buttonSummeryCalculation: { sum: true, average: false },
            buttonSequence: i,
            countInc: 1,
            alarmTag: true,
            textNoteTag: true,
            locationTag: false
          }
        });
      }

      const startTime = Date.now();
      
      const response = await request(app)
        .post('/api/v1/app/sync')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ operations })
        .expect(200);

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Performance assertions
      expect(duration).toBeLessThan(5000); // Should complete in under 5 seconds
      expect(response.body.data).toHaveLength(100);
      
      // Verify all operations were processed
      response.body.data.forEach((result, index) => {
        expect(result.operationType).toBe('ADD_BUTTON');
        expect(result.syncId).toBe(index + 1);
        expect(result.btnId).toBeDefined();
        expect(result.alarmId).toBeDefined();
      });

      console.log(`Bulk button creation: ${100} operations in ${duration}ms`);
    });

    test('should handle mixed button operations in dependency order', async () => {
      const operations = [
        // Create buttons first
        {
          operationType: 'ADD_BUTTON',
          syncId: 1,
          localButtonId: 101,
          payload: {
            buttonName: 'Parent Button',
            buttonColor: '#00FF00',
            buttonType: 2, // DURATION
            buttonShape: 'SQUARE',
            buttonSummeryCalculation: { sum: true },
            buttonSequence: 1
          }
        },
        // Then create items
        {
          operationType: 'CREATE_DURATION_BUTTON_ITEM',
          syncId: 2,
          localButtonId: 101,
          localItemId: 201,
          payload: {
            buttonType: 2,
            displayTime: '10:30',
            displayDate: '2024-01-15',
            displayMonthYear: 'Jan 2024',
            durationStartTimeStamp: '1705123800000',
            durationStopTimeStamp: '1705127400000',
            durationTimeMs: 3600000
          }
        },
        // Then create tags
        {
          operationType: 'CREATE_ITEM_TAG',
          syncId: 3,
          localButtonId: 101,
          localItemId: 201,
          localTagId: 301,
          payload: {
            tagType: 'NOTE',
            tagTitle: 'Test Note',
            tagValue: 'This is a test note',
            tagTimeStamp: '1705123800000'
          }
        }
      ];

      const response = await request(app)
        .post('/api/v1/app/sync')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ operations })
        .expect(200);

      expect(response.body.data).toHaveLength(3);
      
      // Verify dependency order was maintained
      const buttonResult = response.body.data.find(r => r.operationType === 'ADD_BUTTON');
      const itemResult = response.body.data.find(r => r.operationType === 'CREATE_DURATION_BUTTON_ITEM');
      const tagResult = response.body.data.find(r => r.operationType === 'CREATE_ITEM_TAG');

      expect(buttonResult.btnId).toBeDefined();
      expect(itemResult.itemId).toBeDefined();
      expect(tagResult.tagId).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    test('should provide detailed error context for failed operations', async () => {
      const operations = [
        {
          operationType: 'ADD_BUTTON',
          syncId: 1,
          localButtonId: 102,
          payload: {
            buttonName: 'Valid Button',
            buttonColor: '#0000FF',
            buttonType: 1,
            buttonShape: 'CIRCLE',
            buttonSummeryCalculation: { sum: true },
            buttonSequence: 1,
            countInc: 1
          }
        },
        {
          operationType: 'ADD_BUTTON',
          syncId: 2,
          localButtonId: 103,
          payload: {
            buttonName: 'Invalid Button',
            buttonColor: '#FF00FF',
            buttonType: 999, // Invalid button type
            buttonShape: 'CIRCLE',
            buttonSummeryCalculation: { sum: true },
            buttonSequence: 2
          }
        }
      ];

      const response = await request(app)
        .post('/api/v1/app/sync')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ operations })
        .expect(500);

      // Verify error context
      expect(response.body.message).toBeDefined();
      expect(response.body.code).toBe('SYNC_ERROR');
      expect(response.body.operationIndex).toBe(1); // Second operation (0-indexed)
      expect(response.body.operationType).toBe('ADD_BUTTON');
      expect(response.body.timestamp).toBeDefined();
    });

    test('should handle client disconnect gracefully', async () => {
      const operations = [];
      
      // Create a large batch to simulate long processing
      for (let i = 1; i <= 1000; i++) {
        operations.push({
          operationType: 'ADD_BUTTON',
          syncId: i,
          localButtonId: i + 1000,
          payload: {
            buttonName: `Bulk Button ${i}`,
            buttonColor: '#FFFF00',
            buttonType: 1,
            buttonShape: 'CIRCLE',
            buttonSummeryCalculation: { sum: true },
            buttonSequence: i,
            countInc: 1
          }
        });
      }

      // This test would require special setup to simulate client disconnect
      // In a real test environment, you would abort the request mid-processing
      console.log('Client disconnect test would require special test setup');
    });
  });

  describe('Performance Monitoring', () => {
    test('should track performance metrics', async () => {
      const operations = [
        {
          operationType: 'ADD_BUTTON',
          syncId: 1,
          localButtonId: 104,
          payload: {
            buttonName: 'Metrics Test Button',
            buttonColor: '#00FFFF',
            buttonType: 3, // VALUE
            buttonShape: 'SQUARE',
            buttonSummeryCalculation: { sum: true, average: true },
            buttonSequence: 1,
            valueUnit: 'kg',
            valueUnitDescription: 'Weight in kilograms',
            valueItemName: true
          }
        }
      ];

      const startTime = Date.now();
      
      const response = await request(app)
        .post('/api/v1/app/sync')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ operations })
        .expect(200);

      const endTime = Date.now();
      const duration = endTime - startTime;

      // In a real implementation, you would check server logs for performance metrics
      console.log(`Single operation duration: ${duration}ms`);
      
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].btnId).toBeDefined();
    });
  });

  describe('Large Batch Processing', () => {
    test('should handle 5000 operations efficiently', async () => {
      const operations = [];
      
      // Create 5000 mixed operations
      for (let i = 1; i <= 5000; i++) {
        if (i <= 2500) {
          // First half: button creation
          operations.push({
            operationType: 'ADD_BUTTON',
            syncId: i,
            localButtonId: i + 5000,
            payload: {
              buttonName: `Large Batch Button ${i}`,
              buttonColor: '#FF6600',
              buttonType: (i % 3) + 1, // Rotate between 1, 2, 3
              buttonShape: i % 2 === 0 ? 'CIRCLE' : 'SQUARE',
              buttonSummeryCalculation: { sum: true },
              buttonSequence: i,
              countInc: i % 2 === 0 ? 1 : 2
            }
          });
        } else {
          // Second half: item creation (depends on buttons)
          const buttonIndex = i - 2500;
          operations.push({
            operationType: 'CREATE_COUNT_BUTTON_ITEM',
            syncId: i,
            localButtonId: buttonIndex + 5000,
            localItemId: i + 10000,
            payload: {
              buttonType: 1, // COUNT
              displayTime: '12:00',
              displayDate: '2024-01-15',
              displayMonthYear: 'Jan 2024',
              countIncrement: Math.floor(Math.random() * 10) + 1,
              countTimeStamp: Date.now().toString()
            }
          });
        }
      }

      const startTime = Date.now();
      
      const response = await request(app)
        .post('/api/v1/app/sync')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ operations })
        .expect(200);

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Performance assertions for large batch
      expect(duration).toBeLessThan(60000); // Should complete in under 60 seconds
      expect(response.body.data).toHaveLength(5000);

      console.log(`Large batch processing: 5000 operations in ${duration}ms`);
      console.log(`Average: ${(duration / 5000).toFixed(2)}ms per operation`);
      
      // Verify all operations were processed successfully
      const buttonResults = response.body.data.filter(r => r.operationType === 'ADD_BUTTON');
      const itemResults = response.body.data.filter(r => r.operationType === 'CREATE_COUNT_BUTTON_ITEM');
      
      expect(buttonResults).toHaveLength(2500);
      expect(itemResults).toHaveLength(2500);
    }, 120000); // 2 minute timeout for large batch test
  });

  afterAll(async () => {
    // Cleanup test data
    // This would typically involve cleaning up test users and data
  });
});

// Helper function to generate test operations
function generateTestOperations(count, startId = 1) {
  const operations = [];
  
  for (let i = 0; i < count; i++) {
    operations.push({
      operationType: 'ADD_BUTTON',
      syncId: startId + i,
      localButtonId: startId + i + 10000,
      payload: {
        buttonName: `Generated Button ${startId + i}`,
        buttonColor: `#${Math.floor(Math.random()*16777215).toString(16)}`,
        buttonType: (i % 3) + 1,
        buttonShape: i % 2 === 0 ? 'CIRCLE' : 'SQUARE',
        buttonSummeryCalculation: { sum: true, average: i % 2 === 0 },
        buttonSequence: startId + i,
        countInc: Math.floor(Math.random() * 5) + 1
      }
    });
  }
  
  return operations;
}

module.exports = {
  generateTestOperations
};
