/**
 * Performance Benchmark Script for Sync Endpoint Optimization
 * 
 * This script compares the performance of the original vs optimized sync endpoint
 * and provides detailed metrics for analysis.
 */

const axios = require('axios');
const fs = require('fs');

class SyncBenchmark {
  constructor(baseUrl, authToken) {
    this.baseUrl = baseUrl;
    this.authToken = authToken;
    this.results = [];
  }

  // Generate test operations for benchmarking
  generateOperations(count, type = 'mixed') {
    const operations = [];
    
    switch (type) {
      case 'buttons_only':
        for (let i = 1; i <= count; i++) {
          operations.push({
            operationType: 'ADD_BUTTON',
            syncId: i,
            localButtonId: i + Date.now(),
            payload: {
              buttonName: `Benchmark Button ${i}`,
              buttonColor: '#FF0000',
              buttonType: (i % 3) + 1,
              buttonShape: i % 2 === 0 ? 'CIRCLE' : 'SQUARE',
              buttonSummeryCalculation: { sum: true, average: false },
              buttonSequence: i,
              countInc: 1
            }
          });
        }
        break;
        
      case 'mixed':
        // 50% buttons, 30% items, 20% tags
        const buttonCount = Math.floor(count * 0.5);
        const itemCount = Math.floor(count * 0.3);
        const tagCount = count - buttonCount - itemCount;
        
        // Add buttons first
        for (let i = 1; i <= buttonCount; i++) {
          operations.push({
            operationType: 'ADD_BUTTON',
            syncId: i,
            localButtonId: i + Date.now(),
            payload: {
              buttonName: `Mixed Button ${i}`,
              buttonColor: '#00FF00',
              buttonType: 1,
              buttonShape: 'CIRCLE',
              buttonSummeryCalculation: { sum: true },
              buttonSequence: i,
              countInc: 1
            }
          });
        }
        
        // Add items
        for (let i = 1; i <= itemCount; i++) {
          operations.push({
            operationType: 'CREATE_COUNT_BUTTON_ITEM',
            syncId: buttonCount + i,
            localButtonId: i + Date.now(),
            localItemId: i + Date.now() + 10000,
            payload: {
              buttonType: 1,
              displayTime: '10:30',
              displayDate: '2024-01-15',
              displayMonthYear: 'Jan 2024',
              countIncrement: Math.floor(Math.random() * 10) + 1,
              countTimeStamp: Date.now().toString()
            }
          });
        }
        
        // Add tags
        for (let i = 1; i <= tagCount; i++) {
          operations.push({
            operationType: 'CREATE_ITEM_TAG',
            syncId: buttonCount + itemCount + i,
            localButtonId: i + Date.now(),
            localItemId: i + Date.now() + 10000,
            localTagId: i + Date.now() + 20000,
            payload: {
              tagType: 'NOTE',
              tagTitle: `Benchmark Tag ${i}`,
              tagValue: `Test tag value ${i}`,
              tagTimeStamp: Date.now().toString()
            }
          });
        }
        break;
    }
    
    return operations;
  }

  // Run a single benchmark test
  async runBenchmark(operationCount, operationType = 'mixed', iterations = 3) {
    console.log(`\n🚀 Running benchmark: ${operationCount} ${operationType} operations (${iterations} iterations)`);
    
    const results = [];
    
    for (let i = 1; i <= iterations; i++) {
      console.log(`  Iteration ${i}/${iterations}...`);
      
      const operations = this.generateOperations(operationCount, operationType);
      const startTime = Date.now();
      const startMemory = process.memoryUsage();
      
      try {
        const response = await axios.post(`${this.baseUrl}/api/v1/app/sync`, 
          { operations },
          {
            headers: {
              'Authorization': `Bearer ${this.authToken}`,
              'Content-Type': 'application/json'
            },
            timeout: 120000 // 2 minute timeout
          }
        );
        
        const endTime = Date.now();
        const endMemory = process.memoryUsage();
        const duration = endTime - startTime;
        
        const result = {
          iteration: i,
          operationCount,
          operationType,
          duration,
          throughput: (operationCount / duration * 1000).toFixed(2), // ops/sec
          avgLatency: (duration / operationCount).toFixed(2), // ms/op
          memoryUsed: (endMemory.heapUsed - startMemory.heapUsed) / 1024 / 1024, // MB
          success: true,
          responseSize: JSON.stringify(response.data).length
        };
        
        results.push(result);
        console.log(`    ✅ ${duration}ms (${result.throughput} ops/sec)`);
        
      } catch (error) {
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        results.push({
          iteration: i,
          operationCount,
          operationType,
          duration,
          success: false,
          error: error.message
        });
        
        console.log(`    ❌ Failed after ${duration}ms: ${error.message}`);
      }
      
      // Wait between iterations to avoid overwhelming the server
      if (i < iterations) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
    
    return this.calculateStats(results);
  }

  // Calculate statistics from benchmark results
  calculateStats(results) {
    const successfulResults = results.filter(r => r.success);
    
    if (successfulResults.length === 0) {
      return {
        operationCount: results[0].operationCount,
        operationType: results[0].operationType,
        success: false,
        error: 'All iterations failed'
      };
    }
    
    const durations = successfulResults.map(r => r.duration);
    const throughputs = successfulResults.map(r => parseFloat(r.throughput));
    const latencies = successfulResults.map(r => parseFloat(r.avgLatency));
    const memoryUsages = successfulResults.map(r => r.memoryUsed);
    
    return {
      operationCount: results[0].operationCount,
      operationType: results[0].operationType,
      iterations: results.length,
      successfulIterations: successfulResults.length,
      avgDuration: (durations.reduce((a, b) => a + b, 0) / durations.length).toFixed(2),
      minDuration: Math.min(...durations),
      maxDuration: Math.max(...durations),
      avgThroughput: (throughputs.reduce((a, b) => a + b, 0) / throughputs.length).toFixed(2),
      maxThroughput: Math.max(...throughputs).toFixed(2),
      avgLatency: (latencies.reduce((a, b) => a + b, 0) / latencies.length).toFixed(2),
      minLatency: Math.min(...latencies).toFixed(2),
      avgMemoryUsage: (memoryUsages.reduce((a, b) => a + b, 0) / memoryUsages.length).toFixed(2),
      success: true
    };
  }

  // Run comprehensive benchmark suite
  async runBenchmarkSuite() {
    console.log('🔥 Starting Sync Endpoint Benchmark Suite');
    console.log('==========================================');
    
    const testCases = [
      { count: 10, type: 'buttons_only', iterations: 3 },
      { count: 50, type: 'buttons_only', iterations: 3 },
      { count: 100, type: 'buttons_only', iterations: 3 },
      { count: 500, type: 'buttons_only', iterations: 2 },
      { count: 1000, type: 'buttons_only', iterations: 2 },
      { count: 10, type: 'mixed', iterations: 3 },
      { count: 50, type: 'mixed', iterations: 3 },
      { count: 100, type: 'mixed', iterations: 3 },
      { count: 500, type: 'mixed', iterations: 2 },
      { count: 1000, type: 'mixed', iterations: 2 },
      { count: 2500, type: 'mixed', iterations: 1 },
      { count: 5000, type: 'mixed', iterations: 1 }
    ];
    
    for (const testCase of testCases) {
      const result = await this.runBenchmark(testCase.count, testCase.type, testCase.iterations);
      this.results.push(result);
      
      // Wait between test cases
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
    
    this.generateReport();
  }

  // Generate detailed benchmark report
  generateReport() {
    console.log('\n📊 BENCHMARK RESULTS SUMMARY');
    console.log('============================');
    
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalTests: this.results.length,
        successfulTests: this.results.filter(r => r.success).length,
        failedTests: this.results.filter(r => !r.success).length
      },
      results: this.results
    };
    
    // Console output
    console.table(this.results.map(r => ({
      'Operations': r.operationCount,
      'Type': r.operationType,
      'Avg Duration (ms)': r.avgDuration || 'FAILED',
      'Throughput (ops/sec)': r.avgThroughput || 'FAILED',
      'Avg Latency (ms/op)': r.avgLatency || 'FAILED',
      'Memory (MB)': r.avgMemoryUsage || 'FAILED',
      'Status': r.success ? '✅' : '❌'
    })));
    
    // Performance insights
    console.log('\n🎯 PERFORMANCE INSIGHTS');
    console.log('=======================');
    
    const successfulResults = this.results.filter(r => r.success);
    if (successfulResults.length > 0) {
      const bestThroughput = Math.max(...successfulResults.map(r => parseFloat(r.avgThroughput)));
      const bestLatency = Math.min(...successfulResults.map(r => parseFloat(r.avgLatency)));
      
      console.log(`🚀 Best Throughput: ${bestThroughput} operations/second`);
      console.log(`⚡ Best Latency: ${bestLatency} ms/operation`);
      
      // Find performance degradation points
      const buttonTests = successfulResults.filter(r => r.operationType === 'buttons_only').sort((a, b) => a.operationCount - b.operationCount);
      if (buttonTests.length > 1) {
        console.log('\n📈 Scalability Analysis (Button Operations):');
        buttonTests.forEach(test => {
          console.log(`  ${test.operationCount} ops: ${test.avgThroughput} ops/sec, ${test.avgLatency} ms/op`);
        });
      }
    }
    
    // Save detailed report to file
    const filename = `benchmark_report_${Date.now()}.json`;
    fs.writeFileSync(filename, JSON.stringify(report, null, 2));
    console.log(`\n💾 Detailed report saved to: ${filename}`);
    
    return report;
  }
}

// Example usage
async function runBenchmarks() {
  const benchmark = new SyncBenchmark('http://localhost:3000', 'your-auth-token-here');
  
  try {
    await benchmark.runBenchmarkSuite();
  } catch (error) {
    console.error('Benchmark failed:', error);
  }
}

// Run if called directly
if (require.main === module) {
  runBenchmarks();
}

module.exports = SyncBenchmark;
